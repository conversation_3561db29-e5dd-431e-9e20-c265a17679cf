"""
Mem0 + Qwen3:1.7B 快速测试版本
使用更小更快的1.7b模型，在速度和效果之间取得平衡
适合日常使用和快速原型开发
"""

import os
import time
from mem0 import Memory
from typing import Dict, List

def create_qwen3_1_7b_config():
    """
    创建使用qwen3:1.7b模型的Mem0配置
    1.7b参数模型在速度和效果之间取得很好的平衡
    """
    config = {
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "qwen3_1_7b_memories",
                "path": "./qwen3_1_7b_chroma_db"
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:1.7b",
                "temperature": 0.1,  # 较低温度确保一致性
                "max_tokens": 1500,  # 适中的token限制
                "ollama_base_url": "http://localhost:11434"
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:1.7b",  # 使用同一模型进行嵌入
                "ollama_base_url": "http://localhost:11434"
            }
        }
    }
    return config

def test_speed_and_accuracy():
    """测试速度和准确性"""
    print("=== Mem0 + Qwen3:1.7B 快速测试 ===\n")
    
    # 初始化Memory对象
    print("1. 初始化Memory对象...")
    config = create_qwen3_1_7b_config()
    
    start_time = time.time()
    try:
        memory = Memory.from_config(config)
        init_time = time.time() - start_time
        print(f"✓ Memory对象初始化成功 (耗时: {init_time:.2f}秒)")
        print(f"✓ 使用模型: qwen3:1.7b")
        print(f"✓ 模型大小: ~1.4GB")
        print()
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return None
    
    return memory

def test_chinese_conversations(memory):
    """测试中文对话处理"""
    print("2. 测试中文对话处理...")
    user_id = "fast_user"
    
    # 准备测试对话
    conversations = [
        {
            "messages": [
                {"role": "user", "content": "我是张伟，在北京工作，是一名软件开发工程师。"},
                {"role": "assistant", "content": "你好张伟！记住了你在北京从事软件开发工作。"}
            ],
            "metadata": {"category": "个人信息"}
        },
        {
            "messages": [
                {"role": "user", "content": "我喜欢喝绿茶，平时爱看科技新闻，周末喜欢打篮球。"},
                {"role": "assistant", "content": "了解了你的兴趣爱好：绿茶、科技新闻、篮球。"}
            ],
            "metadata": {"category": "兴趣爱好"}
        },
        {
            "messages": [
                {"role": "user", "content": "我正在学习人工智能，特别是机器学习和深度学习。"},
                {"role": "assistant", "content": "记住了你在学习AI相关技术。"}
            ],
            "metadata": {"category": "学习内容"}
        }
    ]
    
    # 测试添加记忆的速度
    total_add_time = 0
    for i, conv in enumerate(conversations, 1):
        start_time = time.time()
        try:
            result = memory.add(conv["messages"], user_id=user_id, metadata=conv["metadata"])
            add_time = time.time() - start_time
            total_add_time += add_time
            
            print(f"✓ 添加记忆 {i} (耗时: {add_time:.2f}秒)")
            print(f"  提取的记忆: {[r.get('memory', 'N/A') for r in result.get('results', [])]}")
        except Exception as e:
            print(f"❌ 添加记忆 {i} 失败: {e}")
    
    print(f"\n总添加时间: {total_add_time:.2f}秒 (平均: {total_add_time/len(conversations):.2f}秒/条)")
    print()
    
    return user_id

def test_search_performance(memory, user_id):
    """测试搜索性能"""
    print("3. 测试搜索性能...")
    
    # 准备搜索查询
    search_queries = [
        "张伟的职业是什么？",
        "用户喜欢什么运动？",
        "用户在学习什么技术？",
        "用户喜欢喝什么？",
        "用户在哪里工作？"
    ]
    
    total_search_time = 0
    for i, query in enumerate(search_queries, 1):
        start_time = time.time()
        try:
            results = memory.search(query, user_id=user_id, limit=2)
            search_time = time.time() - start_time
            total_search_time += search_time
            
            print(f"🔍 搜索 {i}: {query} (耗时: {search_time:.2f}秒)")
            for j, result in enumerate(results.get("results", [])[:2], 1):
                print(f"  {j}. {result['memory']} (相关性: {result['score']:.0f})")
            print()
        except Exception as e:
            print(f"❌ 搜索 {i} 失败: {e}")
    
    print(f"总搜索时间: {total_search_time:.2f}秒 (平均: {total_search_time/len(search_queries):.2f}秒/次)")
    print()

def test_practical_scenario(memory):
    """测试实际应用场景"""
    print("4. 测试实际应用场景...")
    
    user_id = "practical_user"
    
    # 模拟客服对话场景
    customer_service_conversation = [
        {"role": "user", "content": "你好，我想咨询一下你们的笔记本电脑。"},
        {"role": "assistant", "content": "您好！很高兴为您介绍我们的笔记本电脑产品。"},
        {"role": "user", "content": "我主要用来编程和设计，预算在8000-10000元之间。"},
        {"role": "assistant", "content": "了解您的需求：编程和设计用途，预算8000-10000元。"},
        {"role": "user", "content": "我比较看重性能和屏幕质量，品牌倾向于联想或华为。"},
        {"role": "assistant", "content": "记住了您的偏好：重视性能和屏幕，偏好联想或华为品牌。"},
        {"role": "user", "content": "我的联系方式是手机13812345678，邮箱是*****************。"},
        {"role": "assistant", "content": "已记录您的联系方式，稍后会有专人为您推荐合适的产品。"}
    ]
    
    start_time = time.time()
    try:
        result = memory.add(
            customer_service_conversation, 
            user_id=user_id,
            metadata={
                "scenario": "客服咨询",
                "product_category": "笔记本电脑",
                "budget_range": "8000-10000",
                "contact_provided": True
            }
        )
        add_time = time.time() - start_time
        
        print(f"✓ 客服对话记忆添加成功 (耗时: {add_time:.2f}秒)")
        print(f"提取的关键信息数量: {len(result.get('results', []))}")
        
        # 测试相关查询
        queries = [
            "客户的预算是多少？",
            "客户偏好什么品牌？",
            "客户的联系方式是什么？",
            "客户主要用途是什么？"
        ]
        
        print("\n相关查询测试:")
        for query in queries:
            start_time = time.time()
            results = memory.search(query, user_id=user_id, limit=1)
            search_time = time.time() - start_time
            
            print(f"🔍 {query} (耗时: {search_time:.2f}秒)")
            if results.get("results"):
                print(f"  答案: {results['results'][0]['memory']}")
            else:
                print("  未找到相关信息")
        
        # 清理测试数据
        memory.delete_all(user_id=user_id)
        print(f"\n✓ 实际场景测试完成，数据已清理")
        
    except Exception as e:
        print(f"❌ 实际场景测试失败: {e}")

def performance_comparison():
    """性能对比总结"""
    print("\n5. 性能对比总结...")
    
    print("📊 Qwen3模型对比:")
    print("┌─────────────┬──────────┬──────────┬──────────────┬──────────────┐")
    print("│    模型     │   大小   │   参数   │   推理速度   │   理解能力   │")
    print("├─────────────┼──────────┼──────────┼──────────────┼──────────────┤")
    print("│ qwen3:0.6b  │  522MB   │   0.6B   │     最快     │     基础     │")
    print("│ qwen3:1.7b  │  1.4GB   │   1.7B   │     快速     │     良好     │")
    print("│ qwen3:4b    │  2.5GB   │   4.0B   │     中等     │     优秀     │")
    print("└─────────────┴──────────┴──────────┴──────────────┴──────────────┘")
    
    print("\n💡 选择建议:")
    print("- 🚀 qwen3:1.7b: 推荐用于日常使用，速度和效果平衡最佳")
    print("- ⚡ qwen3:0.6b: 适合快速测试和资源受限环境")
    print("- 🧠 qwen3:4b: 适合对准确性要求较高的应用")

def main():
    """主函数"""
    print("Mem0 + Qwen3:1.7B 快速测试套件")
    print("=" * 50)
    print("使用1.7b模型在速度和效果之间取得最佳平衡")
    print("=" * 50)
    
    # 检查Ollama服务
    import subprocess
    try:
        result = subprocess.run(['pgrep', 'ollama'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Ollama服务未运行，请先启动: ollama serve")
            return
    except:
        print("⚠️  无法检查Ollama服务状态")
    
    # 运行测试
    try:
        # 基础测试
        memory = test_speed_and_accuracy()
        if not memory:
            return
        
        # 中文对话测试
        user_id = test_chinese_conversations(memory)
        
        # 搜索性能测试
        test_search_performance(memory, user_id)
        
        # 实际应用场景测试
        test_practical_scenario(memory)
        
        # 性能对比
        performance_comparison()
        
        # 清理主测试数据
        memory.delete_all(user_id=user_id)
        
        print("\n" + "=" * 50)
        print("🎉 Qwen3:1.7B 测试完成！")
        print("\n✅ 总结:")
        print("- ✅ 模型加载和初始化速度快")
        print("- ✅ 记忆添加速度显著提升")
        print("- ✅ 搜索响应时间短")
        print("- ✅ 中文理解能力良好")
        print("- ✅ 适合实际应用场景")
        
        print("\n🚀 推荐使用场景:")
        print("- 日常开发和测试")
        print("- 实时对话系统")
        print("- 资源受限环境")
        print("- 快速原型验证")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
