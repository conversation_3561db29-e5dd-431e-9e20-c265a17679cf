"""
Mem0 基础操作测试用例
这个文件演示了Mem0的核心功能：添加、搜索、获取、更新和删除记忆
"""

import os
from dotenv import load_dotenv
from mem0 import Memory

# 加载环境变量
load_dotenv()

def test_basic_memory_operations():
    """
    测试Mem0的基础记忆操作
    包括：添加记忆、搜索记忆、获取所有记忆、更新记忆、删除记忆
    """
    print("=== Mem0 基础操作测试 ===\n")
    
    # 1. 初始化Memory对象
    print("1. 初始化Memory对象...")
    memory = Memory()
    print("✓ Memory对象初始化成功\n")
    
    # 2. 添加记忆 - 使用对话格式
    print("2. 添加记忆...")
    user_id = "alice"
    
    # 添加用户偏好信息
    messages = [
        {"role": "user", "content": "你好，我是Alice。我是素食主义者，对坚果过敏。"},
        {"role": "assistant", "content": "你好Alice！我已经记住你是素食主义者并且对坚果过敏。"}
    ]
    
    result = memory.add(messages, user_id=user_id, metadata={"category": "dietary_preferences"})
    print(f"✓ 添加记忆结果: {result}")
    print()
    
    # 添加更多个人信息
    messages2 = [
        {"role": "user", "content": "我住在北京，喜欢看科幻电影，特别是《星际穿越》。"},
        {"role": "assistant", "content": "好的，我记住了你住在北京，喜欢科幻电影，特别喜欢《星际穿越》。"}
    ]
    
    result2 = memory.add(messages2, user_id=user_id, metadata={"category": "personal_info"})
    print(f"✓ 添加更多记忆: {result2}")
    print()
    
    # 3. 搜索记忆
    print("3. 搜索记忆...")
    
    # 搜索饮食偏好
    search_results = memory.search("Alice的饮食偏好是什么？", user_id=user_id)
    print("搜索结果 - 饮食偏好:")
    for i, result in enumerate(search_results.get("results", []), 1):
        print(f"  {i}. 记忆: {result['memory']}")
        print(f"     分数: {result['score']:.4f}")
        print(f"     元数据: {result.get('metadata', {})}")
        print()
    
    # 搜索电影偏好
    movie_results = memory.search("Alice喜欢什么电影？", user_id=user_id)
    print("搜索结果 - 电影偏好:")
    for i, result in enumerate(movie_results.get("results", []), 1):
        print(f"  {i}. 记忆: {result['memory']}")
        print(f"     分数: {result['score']:.4f}")
        print()
    
    # 4. 获取所有记忆
    print("4. 获取所有记忆...")
    all_memories = memory.get_all(user_id=user_id)
    print(f"用户 {user_id} 的所有记忆:")
    for i, mem in enumerate(all_memories, 1):
        print(f"  {i}. ID: {mem['id']}")
        print(f"     内容: {mem['memory']}")
        print(f"     创建时间: {mem.get('created_at', 'N/A')}")
        print(f"     元数据: {mem.get('metadata', {})}")
        print()
    
    # 5. 更新记忆（如果有记忆的话）
    if all_memories:
        print("5. 更新记忆...")
        first_memory_id = all_memories[0]['id']
        update_result = memory.update(
            memory_id=first_memory_id,
            data="Alice是严格的素食主义者，对所有坚果都过敏，包括花生。"
        )
        print(f"✓ 更新记忆结果: {update_result}")
        print()
        
        # 验证更新
        updated_memory = memory.get(first_memory_id)
        print(f"更新后的记忆: {updated_memory}")
        print()
    
    # 6. 删除特定记忆
    if len(all_memories) > 1:
        print("6. 删除特定记忆...")
        second_memory_id = all_memories[1]['id']
        delete_result = memory.delete(second_memory_id)
        print(f"✓ 删除记忆结果: {delete_result}")
        print()
    
    # 7. 清理 - 删除所有记忆
    print("7. 清理所有记忆...")
    cleanup_result = memory.delete_all(user_id=user_id)
    print(f"✓ 清理结果: {cleanup_result}")
    print()
    
    print("=== 基础操作测试完成 ===")

if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        print("错误: 请设置OPENAI_API_KEY环境变量")
        print("请复制.env.example为.env并填入你的API密钥")
        exit(1)
    
    test_basic_memory_operations()
