"""
Mem0 学习测试套件运行器
这个脚本帮助你按顺序运行所有的Mem0学习测试用例
"""

import os
import sys
import subprocess
from pathlib import Path

def check_environment():
    """检查环境配置"""
    print("=== 环境检查 ===")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or python_version.minor < 8:
        print("❌ 需要Python 3.8或更高版本")
        return False
    else:
        print("✓ Python版本符合要求")
    
    # 检查依赖包
    try:
        import mem0
        print(f"✓ mem0ai 已安装 (版本: {mem0.__version__})")
    except ImportError:
        print("❌ mem0ai 未安装，请运行: pip install mem0ai")
        return False
    
    try:
        import openai
        print(f"✓ openai 已安装")
    except ImportError:
        print("❌ openai 未安装，请运行: pip install openai")
        return False
    
    try:
        import dotenv
        print("✓ python-dotenv 已安装")
    except ImportError:
        print("❌ python-dotenv 未安装，请运行: pip install python-dotenv")
        return False
    
    # 检查环境变量文件
    env_file = Path(".env")
    if env_file.exists():
        print("✓ .env 文件存在")
        
        # 检查API密钥
        from dotenv import load_dotenv
        load_dotenv()
        
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key and openai_key != "sk-your-openai-api-key-here":
            print("✓ OPENAI_API_KEY 已配置")
            return True
        else:
            print("⚠️  OPENAI_API_KEY 未配置或使用默认值")
            print("   某些测试需要真实的API密钥才能运行")
            return "partial"
    else:
        print("⚠️  .env 文件不存在")
        print("   请复制 .env.example 为 .env 并配置API密钥")
        return "partial"

def run_script(script_name, description):
    """运行指定的脚本"""
    print(f"\n{'='*60}")
    print(f"运行: {script_name}")
    print(f"描述: {description}")
    print('='*60)
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=False, 
                              text=True, 
                              cwd=Path.cwd())
        
        if result.returncode == 0:
            print(f"\n✓ {script_name} 运行成功")
            return True
        else:
            print(f"\n❌ {script_name} 运行失败 (退出码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"\n❌ 运行 {script_name} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("Mem0 学习测试套件")
    print("=" * 50)
    
    # 检查环境
    env_status = check_environment()
    
    if env_status == False:
        print("\n❌ 环境检查失败，请先解决上述问题")
        return
    
    print("\n" + "=" * 50)
    print("开始运行测试用例...")
    
    # 定义测试脚本列表
    test_scripts = [
        {
            "script": "demo_without_api.py",
            "description": "Mem0概念演示 (无需API密钥)",
            "required_api": False
        },
        {
            "script": "01_basic_memory_operations.py", 
            "description": "基础记忆操作测试",
            "required_api": True
        },
        {
            "script": "02_advanced_configuration.py",
            "description": "高级配置测试", 
            "required_api": True
        },
        {
            "script": "03_memory_chat_system.py",
            "description": "智能聊天系统测试",
            "required_api": True
        },
        {
            "script": "04_multi_user_memory.py",
            "description": "多用户记忆管理测试",
            "required_api": True
        }
    ]
    
    # 运行测试
    results = []
    
    for test in test_scripts:
        script_path = Path(test["script"])
        
        if not script_path.exists():
            print(f"\n⚠️  脚本 {test['script']} 不存在，跳过")
            results.append({"script": test["script"], "status": "skipped", "reason": "文件不存在"})
            continue
        
        # 检查是否需要API密钥
        if test["required_api"] and env_status == "partial":
            print(f"\n⚠️  {test['script']} 需要API密钥，但未配置，跳过")
            results.append({"script": test["script"], "status": "skipped", "reason": "需要API密钥"})
            continue
        
        # 询问是否运行
        print(f"\n准备运行: {test['script']} - {test['description']}")
        if test["required_api"]:
            print("⚠️  此测试需要API密钥，可能产生费用")
        
        choice = input("是否运行此测试？ (y/n/q): ").strip().lower()
        
        if choice == 'q':
            print("用户选择退出")
            break
        elif choice == 'n':
            print("跳过此测试")
            results.append({"script": test["script"], "status": "skipped", "reason": "用户跳过"})
            continue
        elif choice == 'y' or choice == '':
            success = run_script(test["script"], test["description"])
            results.append({
                "script": test["script"], 
                "status": "success" if success else "failed",
                "reason": ""
            })
        else:
            print("无效选择，跳过此测试")
            results.append({"script": test["script"], "status": "skipped", "reason": "无效选择"})
    
    # 显示总结
    print("\n" + "=" * 60)
    print("测试运行总结")
    print("=" * 60)
    
    for result in results:
        status_icon = {
            "success": "✓",
            "failed": "❌", 
            "skipped": "⚠️"
        }.get(result["status"], "?")
        
        reason = f" ({result['reason']})" if result["reason"] else ""
        print(f"{status_icon} {result['script']}: {result['status']}{reason}")
    
    # 统计
    success_count = sum(1 for r in results if r["status"] == "success")
    failed_count = sum(1 for r in results if r["status"] == "failed")
    skipped_count = sum(1 for r in results if r["status"] == "skipped")
    
    print(f"\n总计: {len(results)} 个测试")
    print(f"成功: {success_count}")
    print(f"失败: {failed_count}")
    print(f"跳过: {skipped_count}")
    
    if failed_count > 0:
        print(f"\n❌ 有 {failed_count} 个测试失败，请检查错误信息")
    elif success_count > 0:
        print(f"\n🎉 所有运行的测试都成功完成！")
    
    print("\n感谢使用Mem0学习测试套件！")
    print("更多信息请参考: https://docs.mem0.ai/")

if __name__ == "__main__":
    main()
