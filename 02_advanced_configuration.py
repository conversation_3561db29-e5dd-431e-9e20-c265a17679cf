"""
Mem0 高级配置测试用例
演示如何配置不同的向量数据库、LLM提供商和嵌入模型
"""

import os
from dotenv import load_dotenv
from mem0 import Memory

# 加载环境变量
load_dotenv()

def test_chroma_configuration():
    """
    测试使用ChromaDB作为向量数据库的配置
    ChromaDB是一个轻量级的向量数据库，适合本地开发
    """
    print("=== 测试ChromaDB配置 ===\n")
    
    # ChromaDB配置
    config = {
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "mem0_test_collection",
                "path": "./chroma_db",  # 本地存储路径
                "allow_reset": True
            }
        },
        "llm": {
            "provider": "openai",
            "config": {
                "model": "gpt-4o-mini",
                "temperature": 0.1,
                "max_tokens": 2000
            }
        },
        "embedder": {
            "provider": "openai",
            "config": {
                "model": "text-embedding-3-small",
                "embedding_dims": 1536
            }
        }
    }
    
    print("配置信息:")
    print(f"- 向量数据库: ChromaDB (本地存储)")
    print(f"- LLM: OpenAI GPT-4o-mini")
    print(f"- 嵌入模型: OpenAI text-embedding-3-small")
    print()
    
    # 初始化Memory对象
    memory = Memory.from_config(config)
    
    # 测试添加和搜索
    user_id = "test_user_chroma"
    messages = [
        {"role": "user", "content": "我喜欢喝咖啡，特别是拿铁。"},
        {"role": "assistant", "content": "我记住了你喜欢咖啡，特别是拿铁。"}
    ]
    
    result = memory.add(messages, user_id=user_id)
    print(f"✓ 添加记忆成功: {result}")
    
    # 搜索测试
    search_results = memory.search("用户喜欢什么饮品？", user_id=user_id)
    print(f"✓ 搜索结果: {search_results}")
    
    # 清理
    memory.delete_all(user_id=user_id)
    print("✓ ChromaDB配置测试完成\n")

def test_custom_llm_configuration():
    """
    测试自定义LLM配置
    演示如何配置不同的模型参数
    """
    print("=== 测试自定义LLM配置 ===\n")
    
    # 自定义LLM配置
    config = {
        "llm": {
            "provider": "openai",
            "config": {
                "model": "gpt-4o",  # 使用更强大的模型
                "temperature": 0.2,  # 较低的温度，更确定性的输出
                "max_tokens": 3000,  # 更多的token限制
                "top_p": 0.9
            }
        },
        "embedder": {
            "provider": "openai",
            "config": {
                "model": "text-embedding-3-large",  # 使用更大的嵌入模型
                "embedding_dims": 3072  # 对应的维度
            }
        },
        # 自定义提示词
        "custom_fact_extraction_prompt": "请从对话中提取重要的事实信息，特别关注用户的偏好、习惯和个人信息。",
        "custom_update_memory_prompt": "请智能地更新记忆，合并相似信息，删除过时信息。"
    }
    
    print("自定义配置:")
    print(f"- LLM模型: GPT-4o (更强大)")
    print(f"- 温度: 0.2 (更确定性)")
    print(f"- 嵌入模型: text-embedding-3-large (更高维度)")
    print(f"- 自定义提示词: 已配置")
    print()
    
    memory = Memory.from_config(config)
    
    # 测试复杂对话的记忆提取
    user_id = "test_user_custom"
    complex_messages = [
        {"role": "user", "content": "我是一名软件工程师，在北京工作。我平时喜欢阅读技术书籍，特别是关于人工智能和机器学习的。周末我喜欢去咖啡厅工作，通常选择安静的角落。"},
        {"role": "assistant", "content": "我了解了你的职业和兴趣爱好。你是软件工程师，对AI/ML技术书籍感兴趣，喜欢在安静的咖啡厅工作。"}
    ]
    
    result = memory.add(complex_messages, user_id=user_id, metadata={"session": "profile_setup"})
    print(f"✓ 复杂信息添加成功: {result}")
    
    # 测试精确搜索
    search_results = memory.search("用户的工作和兴趣是什么？", user_id=user_id)
    print("搜索结果:")
    for result in search_results.get("results", []):
        print(f"- {result['memory']} (分数: {result['score']:.4f})")
    
    # 清理
    memory.delete_all(user_id=user_id)
    print("✓ 自定义LLM配置测试完成\n")

def test_metadata_and_filtering():
    """
    测试元数据和过滤功能
    演示如何使用元数据来组织和过滤记忆
    """
    print("=== 测试元数据和过滤功能 ===\n")
    
    memory = Memory()
    user_id = "test_user_metadata"
    
    # 添加不同类别的记忆
    categories = [
        {
            "messages": [
                {"role": "user", "content": "我喜欢意大利菜，特别是披萨和意面。"},
                {"role": "assistant", "content": "记住了你喜欢意大利菜。"}
            ],
            "metadata": {"category": "food_preferences", "cuisine": "italian", "priority": "high"}
        },
        {
            "messages": [
                {"role": "user", "content": "我在学习Python编程，目前在做一个机器学习项目。"},
                {"role": "assistant", "content": "了解你在学习Python和机器学习。"}
            ],
            "metadata": {"category": "learning", "subject": "programming", "priority": "medium"}
        },
        {
            "messages": [
                {"role": "user", "content": "我计划下个月去日本旅行，想去东京和京都。"},
                {"role": "assistant", "content": "记住了你的日本旅行计划。"}
            ],
            "metadata": {"category": "travel_plans", "destination": "japan", "priority": "high"}
        }
    ]
    
    # 添加所有记忆
    for i, item in enumerate(categories):
        result = memory.add(
            item["messages"], 
            user_id=user_id, 
            metadata=item["metadata"]
        )
        print(f"✓ 添加记忆 {i+1}: {result}")
    
    print()
    
    # 获取所有记忆并查看元数据
    all_memories = memory.get_all(user_id=user_id)
    print("所有记忆及其元数据:")
    for i, mem in enumerate(all_memories, 1):
        print(f"{i}. {mem['memory']}")
        print(f"   元数据: {mem.get('metadata', {})}")
        print()
    
    # 测试基于内容的搜索
    print("基于内容的搜索测试:")
    search_queries = [
        "用户喜欢什么食物？",
        "用户在学习什么？",
        "用户的旅行计划是什么？"
    ]
    
    for query in search_queries:
        results = memory.search(query, user_id=user_id)
        print(f"查询: {query}")
        for result in results.get("results", [])[:2]:  # 只显示前2个结果
            print(f"  - {result['memory']} (分数: {result['score']:.4f})")
            print(f"    元数据: {result.get('metadata', {})}")
        print()
    
    # 清理
    memory.delete_all(user_id=user_id)
    print("✓ 元数据和过滤测试完成\n")

def main():
    """主函数，运行所有配置测试"""
    print("Mem0 高级配置测试开始...\n")
    
    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        print("错误: 请设置OPENAI_API_KEY环境变量")
        return
    
    try:
        test_chroma_configuration()
        test_custom_llm_configuration()
        test_metadata_and_filtering()
        
        print("=== 所有高级配置测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
