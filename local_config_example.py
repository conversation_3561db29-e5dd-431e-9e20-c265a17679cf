"""
使用Ollama本地模型的Mem0配置示例
需要先安装Ollama并下载模型
"""

from mem0 import Memory

# 本地Ollama配置
config = {
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "local_memories",
            "path": "./local_chroma_db"
        }
    },
    "llm": {
        "provider": "ollama",
        "config": {
            "model": "llama3.1:latest",
            "temperature": 0.1,
            "ollama_base_url": "http://localhost:11434"
        }
    },
    "embedder": {
        "provider": "ollama", 
        "config": {
            "model": "nomic-embed-text:latest",
            "ollama_base_url": "http://localhost:11434"
        }
    }
}

# 使用配置初始化Memory
memory = Memory.from_config(config)

# 测试添加记忆
messages = [
    {"role": "user", "content": "我喜欢喝茶，特别是绿茶。"},
    {"role": "assistant", "content": "记住了你喜欢绿茶。"}
]

result = memory.add(messages, user_id="local_user")
print("本地记忆添加结果:", result)
