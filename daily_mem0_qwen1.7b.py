"""
日常使用的Mem0 + Qwen3:1.7B 配置
快速、轻量、实用的记忆系统
"""

from mem0 import Memory
import time

class FastMemorySystem:
    """快速记忆系统类"""
    
    def __init__(self):
        """初始化快速记忆系统"""
        self.config = {
            "vector_store": {
                "provider": "chroma",
                "config": {
                    "collection_name": "daily_memories",
                    "path": "./daily_chroma_db"
                }
            },
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:1.7b",
                    "temperature": 0.1,
                    "max_tokens": 1000,
                    "ollama_base_url": "http://localhost:11434"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:1.7b",
                    "ollama_base_url": "http://localhost:11434"
                }
            }
        }
        
        print("正在初始化快速记忆系统...")
        start_time = time.time()
        self.memory = Memory.from_config(self.config)
        init_time = time.time() - start_time
        print(f"✓ 系统初始化完成 (耗时: {init_time:.2f}秒)")
        print(f"✓ 使用模型: qwen3:1.7b (1.4GB)")
        print()
    
    def add_conversation(self, user_message: str, assistant_message: str, user_id: str = "default_user", category: str = "general"):
        """添加对话记忆"""
        messages = [
            {"role": "user", "content": user_message},
            {"role": "assistant", "content": assistant_message}
        ]
        
        start_time = time.time()
        try:
            result = self.memory.add(
                messages, 
                user_id=user_id, 
                metadata={"category": category, "timestamp": time.time()}
            )
            add_time = time.time() - start_time
            
            print(f"✓ 记忆已添加 (耗时: {add_time:.2f}秒)")
            extracted_count = len(result.get('results', []))
            if extracted_count > 0:
                print(f"  提取了 {extracted_count} 条关键信息")
            return True
        except Exception as e:
            print(f"❌ 添加记忆失败: {e}")
            return False
    
    def search_memory(self, query: str, user_id: str = "default_user", limit: int = 3):
        """搜索记忆"""
        start_time = time.time()
        try:
            results = self.memory.search(query, user_id=user_id, limit=limit)
            search_time = time.time() - start_time
            
            print(f"🔍 搜索: {query} (耗时: {search_time:.2f}秒)")
            
            memories = results.get("results", [])
            if memories:
                for i, result in enumerate(memories, 1):
                    print(f"  {i}. {result['memory']}")
                    print(f"     相关性: {result['score']:.0f}")
                return memories
            else:
                print("  未找到相关记忆")
                return []
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            return []
    
    def get_all_memories(self, user_id: str = "default_user"):
        """获取所有记忆"""
        try:
            memories = self.memory.get_all(user_id=user_id)
            print(f"📚 用户 {user_id} 的所有记忆 ({len(memories)} 条):")
            for i, mem in enumerate(memories, 1):
                print(f"  {i}. {mem['memory']}")
                category = mem.get('metadata', {}).get('category', 'N/A')
                print(f"     分类: {category}")
            return memories
        except Exception as e:
            print(f"❌ 获取记忆失败: {e}")
            return []
    
    def clear_memories(self, user_id: str = "default_user"):
        """清理记忆"""
        try:
            self.memory.delete_all(user_id=user_id)
            print(f"✓ 用户 {user_id} 的记忆已清理")
            return True
        except Exception as e:
            print(f"❌ 清理失败: {e}")
            return False

def demo_usage():
    """演示使用方法"""
    print("=== 快速记忆系统演示 ===\n")
    
    # 初始化系统
    memory_system = FastMemorySystem()
    
    # 演示添加记忆
    print("1. 添加记忆演示:")
    memory_system.add_conversation(
        "我是李华，在上海工作，喜欢喝咖啡和阅读。",
        "很高兴认识你李华！记住了你在上海工作，喜欢咖啡和阅读。",
        category="个人信息"
    )
    
    memory_system.add_conversation(
        "我正在学习Python编程，特别是机器学习方面。",
        "了解了，你在学习Python和机器学习。",
        category="学习内容"
    )
    
    memory_system.add_conversation(
        "我计划下个月去北京出差，需要待一周。",
        "记住了你的出差计划：下个月去北京一周。",
        category="计划安排"
    )
    
    print()
    
    # 演示搜索记忆
    print("2. 搜索记忆演示:")
    memory_system.search_memory("李华的工作地点在哪里？")
    print()
    
    memory_system.search_memory("用户在学习什么技术？")
    print()
    
    memory_system.search_memory("用户的出差计划是什么？")
    print()
    
    # 显示所有记忆
    print("3. 所有记忆:")
    memory_system.get_all_memories()
    print()
    
    # 清理演示数据
    print("4. 清理演示数据:")
    memory_system.clear_memories()

def interactive_mode():
    """交互模式"""
    print("\n=== 交互模式 ===")
    print("输入 'quit' 退出，'search:查询内容' 搜索，'show' 显示所有记忆，'clear' 清理记忆")
    print("-" * 50)
    
    memory_system = FastMemorySystem()
    
    while True:
        try:
            user_input = input("\n你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见！")
                break
            elif user_input.lower() == 'show':
                memory_system.get_all_memories()
            elif user_input.lower() == 'clear':
                memory_system.clear_memories()
            elif user_input.startswith('search:'):
                query = user_input[7:].strip()
                if query:
                    memory_system.search_memory(query)
                else:
                    print("请输入搜索内容，格式：search:查询内容")
            elif user_input:
                # 添加用户输入作为记忆
                memory_system.add_conversation(
                    user_input,
                    "我记住了这个信息。"
                )
            
        except KeyboardInterrupt:
            print("\n\n再见！")
            break
        except Exception as e:
            print(f"出现错误: {e}")

def main():
    """主函数"""
    print("Mem0 + Qwen3:1.7B 日常使用版本")
    print("=" * 40)
    print("快速、轻量、实用的本地记忆系统")
    print("=" * 40)
    
    # 检查Ollama服务
    import subprocess
    try:
        result = subprocess.run(['pgrep', 'ollama'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Ollama服务未运行")
            print("请在另一个终端运行: ollama serve")
            return
    except:
        print("⚠️  无法检查Ollama服务状态")
    
    # 选择模式
    print("\n选择模式:")
    print("1. 演示模式 - 查看功能演示")
    print("2. 交互模式 - 实际使用体验")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == "1":
        demo_usage()
    elif choice == "2":
        interactive_mode()
    else:
        print("无效选择，运行演示模式...")
        demo_usage()
    
    print("\n🎉 感谢使用快速记忆系统！")
    print("\n💡 系统特点:")
    print("- ⚡ 快速响应 (搜索 < 0.1秒)")
    print("- 🆓 完全免费")
    print("- 🔒 数据隐私")
    print("- 🇨🇳 中文友好")

if __name__ == "__main__":
    main()
