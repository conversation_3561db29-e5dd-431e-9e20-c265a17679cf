"""
Mem0 多用户记忆管理测试用例
演示如何在多用户环境中管理不同用户的记忆
包括用户隔离、记忆共享、批量操作等功能
"""

import os
from dotenv import load_dotenv
from mem0 import Memory
from typing import Dict, List
import json

# 加载环境变量
load_dotenv()

class MultiUserMemoryManager:
    """
    多用户记忆管理器
    负责管理多个用户的记忆，确保用户间的记忆隔离
    """
    
    def __init__(self):
        """初始化多用户记忆管理器"""
        self.memory = Memory()
        self.users = {}  # 存储用户信息
    
    def register_user(self, user_id: str, user_info: Dict):
        """
        注册新用户
        
        Args:
            user_id: 用户唯一标识
            user_info: 用户信息字典
        """
        self.users[user_id] = user_info
        print(f"✓ 用户 {user_id} 注册成功")
    
    def add_user_memory(self, user_id: str, messages: List[Dict], metadata: Dict = None):
        """
        为特定用户添加记忆
        
        Args:
            user_id: 用户ID
            messages: 对话消息列表
            metadata: 元数据
        """
        if user_id not in self.users:
            raise ValueError(f"用户 {user_id} 未注册")
        
        # 添加用户标识到元数据
        if metadata is None:
            metadata = {}
        metadata["user_id"] = user_id
        metadata["user_name"] = self.users[user_id].get("name", "Unknown")
        
        result = self.memory.add(messages, user_id=user_id, metadata=metadata)
        return result
    
    def search_user_memory(self, user_id: str, query: str, limit: int = 5):
        """
        搜索特定用户的记忆
        
        Args:
            user_id: 用户ID
            query: 搜索查询
            limit: 结果数量限制
        """
        if user_id not in self.users:
            raise ValueError(f"用户 {user_id} 未注册")
        
        return self.memory.search(query, user_id=user_id, limit=limit)
    
    def get_user_memory_stats(self, user_id: str) -> Dict:
        """
        获取用户记忆统计信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            包含统计信息的字典
        """
        if user_id not in self.users:
            raise ValueError(f"用户 {user_id} 未注册")
        
        memories = self.memory.get_all(user_id=user_id)
        
        # 统计不同类别的记忆
        categories = {}
        for mem in memories:
            metadata = mem.get("metadata", {})
            category = metadata.get("category", "uncategorized")
            categories[category] = categories.get(category, 0) + 1
        
        return {
            "user_id": user_id,
            "user_name": self.users[user_id].get("name", "Unknown"),
            "total_memories": len(memories),
            "categories": categories,
            "latest_memory": memories[0]["created_at"] if memories else None
        }
    
    def compare_users(self, user_id1: str, user_id2: str, query: str):
        """
        比较两个用户对同一查询的记忆
        
        Args:
            user_id1: 第一个用户ID
            user_id2: 第二个用户ID
            query: 查询内容
        """
        print(f"=== 比较用户记忆: {query} ===")
        
        # 搜索两个用户的记忆
        results1 = self.search_user_memory(user_id1, query)
        results2 = self.search_user_memory(user_id2, query)
        
        print(f"\n{self.users[user_id1]['name']} 的相关记忆:")
        for result in results1.get("results", [])[:3]:
            print(f"  - {result['memory']} (相关性: {result['score']:.4f})")
        
        print(f"\n{self.users[user_id2]['name']} 的相关记忆:")
        for result in results2.get("results", [])[:3]:
            print(f"  - {result['memory']} (相关性: {result['score']:.4f})")
        
        print()
    
    def cleanup_user(self, user_id: str):
        """清理用户的所有记忆"""
        if user_id in self.users:
            self.memory.delete_all(user_id=user_id)
            del self.users[user_id]
            print(f"✓ 用户 {user_id} 的记忆已清理")

def test_multi_user_memory():
    """测试多用户记忆管理"""
    print("=== Mem0 多用户记忆管理测试 ===\n")
    
    # 初始化多用户记忆管理器
    manager = MultiUserMemoryManager()
    
    # 1. 注册多个用户
    print("1. 注册用户...")
    users_data = [
        {
            "user_id": "alice",
            "info": {"name": "Alice", "role": "软件工程师", "location": "北京"}
        },
        {
            "user_id": "bob", 
            "info": {"name": "Bob", "role": "产品经理", "location": "上海"}
        },
        {
            "user_id": "charlie",
            "info": {"name": "Charlie", "role": "设计师", "location": "深圳"}
        }
    ]
    
    for user_data in users_data:
        manager.register_user(user_data["user_id"], user_data["info"])
    print()
    
    # 2. 为不同用户添加不同的记忆
    print("2. 添加用户记忆...")
    
    # Alice的记忆 - 技术偏好
    alice_memories = [
        {
            "messages": [
                {"role": "user", "content": "我喜欢使用Python和JavaScript编程。"},
                {"role": "assistant", "content": "记住了你的编程语言偏好。"}
            ],
            "metadata": {"category": "技术偏好", "priority": "high"}
        },
        {
            "messages": [
                {"role": "user", "content": "我正在学习机器学习，特别关注深度学习框架PyTorch。"},
                {"role": "assistant", "content": "了解你在学习机器学习和PyTorch。"}
            ],
            "metadata": {"category": "学习内容", "priority": "high"}
        }
    ]
    
    # Bob的记忆 - 产品管理
    bob_memories = [
        {
            "messages": [
                {"role": "user", "content": "我负责移动应用产品，关注用户体验和数据分析。"},
                {"role": "assistant", "content": "记住了你的产品管理职责。"}
            ],
            "metadata": {"category": "工作内容", "priority": "high"}
        },
        {
            "messages": [
                {"role": "user", "content": "我喜欢使用Figma和Sketch进行产品原型设计。"},
                {"role": "assistant", "content": "了解你使用的设计工具。"}
            ],
            "metadata": {"category": "工具偏好", "priority": "medium"}
        }
    ]
    
    # Charlie的记忆 - 设计偏好
    charlie_memories = [
        {
            "messages": [
                {"role": "user", "content": "我专注于UI/UX设计，喜欢简约和现代的设计风格。"},
                {"role": "assistant", "content": "记住了你的设计风格偏好。"}
            ],
            "metadata": {"category": "设计风格", "priority": "high"}
        },
        {
            "messages": [
                {"role": "user", "content": "我经常使用Adobe Creative Suite和Figma。"},
                {"role": "assistant", "content": "了解你的设计工具使用习惯。"}
            ],
            "metadata": {"category": "工具偏好", "priority": "medium"}
        }
    ]
    
    # 添加记忆
    for memory in alice_memories:
        manager.add_user_memory("alice", memory["messages"], memory["metadata"])
    
    for memory in bob_memories:
        manager.add_user_memory("bob", memory["messages"], memory["metadata"])
    
    for memory in charlie_memories:
        manager.add_user_memory("charlie", memory["messages"], memory["metadata"])
    
    print("✓ 所有用户记忆添加完成\n")
    
    # 3. 测试用户记忆隔离
    print("3. 测试用户记忆隔离...")
    
    # 搜索技术相关内容
    print("搜索 '编程语言' 相关记忆:")
    for user_id in ["alice", "bob", "charlie"]:
        results = manager.search_user_memory(user_id, "编程语言")
        user_name = manager.users[user_id]["name"]
        print(f"\n{user_name} 的结果:")
        if results.get("results"):
            for result in results["results"][:2]:
                print(f"  - {result['memory']} (相关性: {result['score']:.4f})")
        else:
            print("  - 无相关记忆")
    print()
    
    # 4. 获取用户统计信息
    print("4. 用户记忆统计...")
    for user_id in ["alice", "bob", "charlie"]:
        stats = manager.get_user_memory_stats(user_id)
        print(f"{stats['user_name']} ({stats['user_id']}):")
        print(f"  - 总记忆数: {stats['total_memories']}")
        print(f"  - 记忆分类: {stats['categories']}")
        print()
    
    # 5. 比较用户记忆
    print("5. 用户记忆比较...")
    manager.compare_users("alice", "bob", "工具使用")
    manager.compare_users("bob", "charlie", "设计")
    
    # 6. 测试批量操作
    print("6. 测试记忆更新...")
    
    # 获取Alice的第一个记忆并更新
    alice_memories = manager.memory.get_all(user_id="alice")
    if alice_memories:
        first_memory_id = alice_memories[0]["id"]
        update_result = manager.memory.update(
            memory_id=first_memory_id,
            data="我是Python和JavaScript专家，现在也在学习Go语言。"
        )
        print(f"✓ 更新Alice的记忆: {update_result}")
    
    # 7. 清理测试数据
    print("\n7. 清理测试数据...")
    for user_id in ["alice", "bob", "charlie"]:
        manager.cleanup_user(user_id)
    
    print("\n=== 多用户记忆管理测试完成 ===")

def test_memory_sharing_scenario():
    """测试记忆共享场景"""
    print("\n=== 记忆共享场景测试 ===\n")
    
    manager = MultiUserMemoryManager()
    
    # 注册团队成员
    team_members = [
        {"user_id": "team_lead", "info": {"name": "团队负责人", "role": "lead"}},
        {"user_id": "developer1", "info": {"name": "开发者1", "role": "developer"}},
        {"user_id": "developer2", "info": {"name": "开发者2", "role": "developer"}}
    ]
    
    for member in team_members:
        manager.register_user(member["user_id"], member["info"])
    
    # 添加项目相关记忆
    project_memories = [
        {
            "user_id": "team_lead",
            "messages": [
                {"role": "user", "content": "我们的项目使用React和Node.js技术栈。"},
                {"role": "assistant", "content": "记住了项目技术栈。"}
            ],
            "metadata": {"category": "项目信息", "shared": True}
        },
        {
            "user_id": "developer1", 
            "messages": [
                {"role": "user", "content": "我负责前端开发，使用React和TypeScript。"},
                {"role": "assistant", "content": "了解你的开发职责。"}
            ],
            "metadata": {"category": "个人职责", "shared": False}
        },
        {
            "user_id": "developer2",
            "messages": [
                {"role": "user", "content": "我负责后端API开发，使用Node.js和MongoDB。"},
                {"role": "assistant", "content": "了解你的后端开发工作。"}
            ],
            "metadata": {"category": "个人职责", "shared": False}
        }
    ]
    
    # 添加记忆
    for memory in project_memories:
        manager.add_user_memory(
            memory["user_id"], 
            memory["messages"], 
            memory["metadata"]
        )
    
    # 搜索项目相关信息
    print("搜索项目技术栈信息:")
    for member in team_members:
        user_id = member["user_id"]
        results = manager.search_user_memory(user_id, "技术栈")
        print(f"\n{member['info']['name']} 的搜索结果:")
        for result in results.get("results", []):
            print(f"  - {result['memory']}")
    
    # 清理
    for member in team_members:
        manager.cleanup_user(member["user_id"])
    
    print("\n✓ 记忆共享场景测试完成")

def main():
    """主函数"""
    if not os.getenv("OPENAI_API_KEY"):
        print("错误: 请设置OPENAI_API_KEY环境变量")
        return
    
    try:
        test_multi_user_memory()
        test_memory_sharing_scenario()
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
