"""
简化的Mem0使用示例
使用本地qwen3:4b模型的最简单用法
"""

from mem0 import Memory

def create_local_memory():
    """创建本地Memory对象"""
    config = {
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "my_memories",
                "path": "./my_chroma_db"
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:4b",
                "temperature": 0.1,
                "ollama_base_url": "http://localhost:11434"
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:4b",
                "ollama_base_url": "http://localhost:11434"
            }
        }
    }
    
    return Memory.from_config(config)

def main():
    """简单的使用示例"""
    print("=== 简化的Mem0使用示例 ===\n")
    
    # 创建Memory对象
    memory = create_local_memory()
    user_id = "my_user"
    
    # 1. 添加记忆
    print("1. 添加记忆...")
    messages = [
        {"role": "user", "content": "我喜欢喝咖啡，特别是拿铁。我住在北京，是一名程序员。"},
        {"role": "assistant", "content": "记住了你的偏好和信息。"}
    ]
    
    result = memory.add(messages, user_id=user_id)
    print(f"添加结果: {result}")
    print()
    
    # 2. 搜索记忆
    print("2. 搜索记忆...")
    search_result = memory.search("用户喜欢什么饮品？", user_id=user_id)
    
    for i, result in enumerate(search_result.get("results", []), 1):
        print(f"{i}. {result['memory']} (相关性: {result['score']:.2f})")
    print()
    
    # 3. 获取所有记忆
    print("3. 所有记忆:")
    all_memories = memory.get_all(user_id=user_id)
    for i, mem in enumerate(all_memories, 1):
        print(f"{i}. {mem['memory']}")
    print()
    
    # 4. 清理（可选）
    # memory.delete_all(user_id=user_id)
    # print("记忆已清理")
    
    print("✓ 示例完成！")

if __name__ == "__main__":
    main()
