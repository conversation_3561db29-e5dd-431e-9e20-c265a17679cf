"""
OpenAI API密钥测试脚本
用于验证API密钥是否有效以及账户状态
"""

import os
from dotenv import load_dotenv
from openai import OpenAI

# 加载环境变量
load_dotenv()

def test_openai_connection():
    """测试OpenAI API连接"""
    print("=== OpenAI API 连接测试 ===\n")
    
    # 检查API密钥
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 未找到OPENAI_API_KEY环境变量")
        return False
    
    if api_key == "sk-your-openai-api-key-here":
        print("❌ API密钥仍为默认值，请设置真实的API密钥")
        return False
    
    print(f"✓ API密钥已配置: {api_key[:20]}...")
    
    # 初始化OpenAI客户端
    try:
        client = OpenAI(api_key=api_key)
        print("✓ OpenAI客户端初始化成功")
    except Exception as e:
        print(f"❌ OpenAI客户端初始化失败: {e}")
        return False
    
    # 测试简单的API调用
    print("\n正在测试API调用...")
    try:
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",  # 使用更便宜的模型
            messages=[
                {"role": "user", "content": "Hello, just testing the API. Please respond with 'API test successful'."}
            ],
            max_tokens=10,
            temperature=0
        )
        
        print("✓ API调用成功!")
        print(f"响应: {response.choices[0].message.content}")
        print(f"使用的模型: {response.model}")
        print(f"消耗的tokens: {response.usage.total_tokens}")
        return True
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        
        # 分析错误类型
        error_str = str(e)
        if "insufficient_quota" in error_str or "429" in error_str:
            print("\n💡 解决方案:")
            print("1. 检查你的OpenAI账户余额: https://platform.openai.com/usage")
            print("2. 确认你的付费计划是否有效")
            print("3. 如果是新账户，可能需要添加付费方式")
            print("4. 考虑使用免费的替代方案（见下方建议）")
        elif "invalid_api_key" in error_str:
            print("\n💡 API密钥无效，请检查:")
            print("1. 密钥是否正确复制")
            print("2. 密钥是否已被撤销")
            print("3. 重新生成新的API密钥")
        
        return False

def suggest_alternatives():
    """建议替代方案"""
    print("\n=== 替代方案建议 ===")
    print("如果OpenAI API配额不足，你可以:")
    print()
    print("1. 🆓 使用本地模型 (Ollama)")
    print("   - 安装Ollama: https://ollama.ai/")
    print("   - 下载模型: ollama pull llama3.1")
    print("   - 配置Mem0使用Ollama (参考02_advanced_configuration.py)")
    print()
    print("2. 🔄 使用其他API提供商")
    print("   - Anthropic Claude")
    print("   - Google Gemini")
    print("   - 阿里云通义千问")
    print()
    print("3. 📚 继续学习概念")
    print("   - 运行demo_without_api.py了解核心概念")
    print("   - 阅读README.md学习理论知识")
    print()
    print("4. 💰 充值OpenAI账户")
    print("   - 访问: https://platform.openai.com/billing")
    print("   - 添加付费方式并充值")

def create_local_config_example():
    """创建本地配置示例"""
    print("\n=== 创建本地配置示例 ===")
    
    local_config = '''"""
使用Ollama本地模型的Mem0配置示例
需要先安装Ollama并下载模型
"""

from mem0 import Memory

# 本地Ollama配置
config = {
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "local_memories",
            "path": "./local_chroma_db"
        }
    },
    "llm": {
        "provider": "ollama",
        "config": {
            "model": "llama3.1:latest",
            "temperature": 0.1,
            "ollama_base_url": "http://localhost:11434"
        }
    },
    "embedder": {
        "provider": "ollama", 
        "config": {
            "model": "nomic-embed-text:latest",
            "ollama_base_url": "http://localhost:11434"
        }
    }
}

# 使用配置初始化Memory
memory = Memory.from_config(config)

# 测试添加记忆
messages = [
    {"role": "user", "content": "我喜欢喝茶，特别是绿茶。"},
    {"role": "assistant", "content": "记住了你喜欢绿茶。"}
]

result = memory.add(messages, user_id="local_user")
print("本地记忆添加结果:", result)
'''
    
    with open("local_config_example.py", "w", encoding="utf-8") as f:
        f.write(local_config)
    
    print("✓ 已创建 local_config_example.py")
    print("  这个文件展示了如何使用本地Ollama模型")

def main():
    """主函数"""
    print("OpenAI API 诊断工具")
    print("=" * 40)
    
    # 测试API连接
    success = test_openai_connection()
    
    if not success:
        suggest_alternatives()
        create_local_config_example()
    else:
        print("\n🎉 API测试成功！你可以正常使用Mem0的完整功能。")
        print("现在可以运行其他测试脚本:")
        print("- python 01_basic_memory_operations.py")
        print("- python run_all_tests.py")

if __name__ == "__main__":
    main()
