# Mem0 故障排除指南

## 🚨 常见问题及解决方案

### 1. OpenAI API 配额不足错误

**错误信息：**
```
openai.RateLimitError: Error code: 429 - You exceeded your current quota
```

**原因：**
- OpenAI账户余额不足
- 达到了API使用限制
- 新账户未添加付费方式

**解决方案：**

#### 方案A: 使用本地模型（推荐，免费）
```bash
# 1. 确保Ollama服务运行
ollama serve

# 2. 运行本地模型版本
python 01_basic_memory_operations_local.py
```

**优势：**
- ✅ 完全免费
- ✅ 数据隐私保护
- ✅ 无网络依赖
- ✅ 无API限制

#### 方案B: 充值OpenAI账户
1. 访问 [OpenAI Billing](https://platform.openai.com/billing)
2. 添加付费方式
3. 充值账户余额

#### 方案C: 使用其他API提供商
- Anthropic Claude
- Google Gemini
- 阿里云通义千问

### 2. 依赖包缺失错误

**错误信息：**
```
The 'chromadb' library is required
```

**解决方案：**
```bash
conda activate mem0_test
pip install chromadb
```

**错误信息：**
```
The 'ollama' library is required
```

**解决方案：**
```bash
pip install ollama
```

### 3. Ollama服务未运行

**错误信息：**
```
❌ Ollama服务未运行
```

**解决方案：**
```bash
# 启动Ollama服务
ollama serve

# 或在后台运行
nohup ollama serve &
```

### 4. 配置验证错误

**错误信息：**
```
Configuration validation error: Extra fields not allowed: allow_reset
```

**解决方案：**
移除不支持的配置字段，使用正确的配置格式：

```python
config = {
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "memories",
            "path": "./chroma_db"
            # 移除 "allow_reset": True
        }
    }
}
```

### 5. 环境变量未设置

**错误信息：**
```
❌ 未找到OPENAI_API_KEY环境变量
```

**解决方案：**
```bash
# 1. 复制环境变量模板
cp .env.example .env

# 2. 编辑.env文件，添加真实的API密钥
# OPENAI_API_KEY=sk-your-actual-api-key-here
```

## 🔧 诊断工具

### 运行API密钥测试
```bash
python test_api_key.py
```

这个脚本会：
- 检查API密钥配置
- 测试API连接
- 提供解决建议

### 运行完整测试套件
```bash
python run_all_tests.py
```

这个脚本会：
- 检查环境配置
- 按顺序运行所有测试
- 提供详细的错误信息

## 📋 测试脚本对比

| 脚本 | API密钥需求 | 功能 | 推荐场景 |
|------|------------|------|----------|
| `demo_without_api.py` | ❌ 不需要 | 概念演示 | 学习基础概念 |
| `01_basic_memory_operations_local.py` | ❌ 不需要 | 本地模型测试 | 免费完整体验 |
| `01_basic_memory_operations.py` | ✅ 需要 | OpenAI模型测试 | 最佳效果体验 |
| `test_api_key.py` | ✅ 需要 | API诊断 | 排查API问题 |

## 🎯 推荐学习路径

### 如果没有API密钥或配额不足：
1. `demo_without_api.py` - 理解概念
2. `01_basic_memory_operations_local.py` - 完整体验
3. 阅读文档学习理论

### 如果有有效的API密钥：
1. `test_api_key.py` - 验证API
2. `run_all_tests.py` - 完整测试套件
3. 自定义开发

## 🆘 获取帮助

如果遇到其他问题：

1. **检查日志输出** - 仔细阅读错误信息
2. **查看官方文档** - [Mem0 Docs](https://docs.mem0.ai/)
3. **GitHub Issues** - [Mem0 GitHub](https://github.com/mem0ai/mem0/issues)
4. **社区讨论** - [GitHub Discussions](https://github.com/mem0ai/mem0/discussions)

## 💡 性能优化建议

### 本地模型优化：
- 使用更大的模型（如果硬件允许）
- 调整温度参数
- 优化嵌入模型选择

### API使用优化：
- 使用更便宜的模型（如gpt-3.5-turbo）
- 减少max_tokens设置
- 批量处理请求

---

**记住：本地模型虽然效果可能不如GPT-4，但完全免费且能让你学习所有核心概念！** 🚀
