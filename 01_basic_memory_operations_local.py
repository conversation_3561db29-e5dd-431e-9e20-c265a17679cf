"""
Mem0 基础操作测试用例 - 本地模型版本
使用本地Ollama模型，无需OpenAI API密钥
演示Mem0的核心功能：添加、搜索、获取、更新和删除记忆
"""

import os
from mem0 import Memory

def test_basic_memory_operations_local():
    """
    使用本地模型测试Mem0的基础记忆操作
    """
    print("=== Mem0 基础操作测试 (本地模型版本) ===\n")
    
    # 1. 配置使用本地Ollama模型
    print("1. 配置本地模型...")
    
    config = {
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "local_test_memories",
                "path": "./local_chroma_db"
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:0.6b",  # 使用你已有的模型
                "temperature": 0.1,
                "max_tokens": 1000,
                "ollama_base_url": "http://localhost:11434"
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:0.6b",  # 同样使用qwen3作为嵌入模型
                "ollama_base_url": "http://localhost:11434"
            }
        }
    }
    
    print("配置信息:")
    print(f"- 向量数据库: ChromaDB (本地存储)")
    print(f"- LLM模型: Ollama qwen3:0.6b")
    print(f"- 嵌入模型: Ollama qwen3:0.6b")
    print()
    
    # 2. 初始化Memory对象
    print("2. 初始化Memory对象...")
    try:
        memory = Memory.from_config(config)
        print("✓ Memory对象初始化成功\n")
    except Exception as e:
        print(f"❌ Memory对象初始化失败: {e}")
        print("请确保Ollama服务正在运行: ollama serve")
        return
    
    # 3. 添加记忆 - 使用对话格式
    print("3. 添加记忆...")
    user_id = "local_test_user"
    
    # 添加用户偏好信息
    messages1 = [
        {"role": "user", "content": "你好，我是张三。我是一名软件工程师，喜欢Python编程。"},
        {"role": "assistant", "content": "你好张三！很高兴认识你，我记住了你是软件工程师并且喜欢Python。"}
    ]
    
    try:
        result1 = memory.add(messages1, user_id=user_id, metadata={"category": "personal_info"})
        print(f"✓ 添加个人信息: {result1}")
        print()
    except Exception as e:
        print(f"❌ 添加记忆失败: {e}")
        return
    
    # 添加更多偏好信息
    messages2 = [
        {"role": "user", "content": "我喜欢喝咖啡，特别是拿铁。我住在北京，周末喜欢去公园散步。"},
        {"role": "assistant", "content": "记住了你的偏好：喜欢拿铁咖啡，住在北京，周末喜欢去公园散步。"}
    ]
    
    try:
        result2 = memory.add(messages2, user_id=user_id, metadata={"category": "preferences"})
        print(f"✓ 添加偏好信息: {result2}")
        print()
    except Exception as e:
        print(f"❌ 添加偏好信息失败: {e}")
    
    # 4. 搜索记忆
    print("4. 搜索记忆...")
    
    try:
        # 搜索个人信息
        search_results = memory.search("张三的职业是什么？", user_id=user_id)
        print("搜索结果 - 职业信息:")
        for i, result in enumerate(search_results.get("results", []), 1):
            print(f"  {i}. 记忆: {result['memory']}")
            print(f"     分数: {result['score']:.4f}")
            print(f"     元数据: {result.get('metadata', {})}")
            print()
        
        # 搜索偏好信息
        preference_results = memory.search("用户喜欢什么饮品？", user_id=user_id)
        print("搜索结果 - 饮品偏好:")
        for i, result in enumerate(preference_results.get("results", []), 1):
            print(f"  {i}. 记忆: {result['memory']}")
            print(f"     分数: {result['score']:.4f}")
            print()
    except Exception as e:
        print(f"❌ 搜索记忆失败: {e}")
    
    # 5. 获取所有记忆
    print("5. 获取所有记忆...")
    try:
        all_memories = memory.get_all(user_id=user_id)
        print(f"用户 {user_id} 的所有记忆:")
        for i, mem in enumerate(all_memories, 1):
            print(f"  {i}. ID: {mem['id']}")
            print(f"     内容: {mem['memory']}")
            print(f"     创建时间: {mem.get('created_at', 'N/A')}")
            print(f"     元数据: {mem.get('metadata', {})}")
            print()
    except Exception as e:
        print(f"❌ 获取记忆失败: {e}")
        all_memories = []
    
    # 6. 更新记忆（如果有记忆的话）
    if all_memories:
        print("6. 更新记忆...")
        try:
            first_memory_id = all_memories[0]['id']
            update_result = memory.update(
                memory_id=first_memory_id,
                data="张三是一名高级Python开发工程师，专注于AI和机器学习项目。"
            )
            print(f"✓ 更新记忆结果: {update_result}")
            print()
            
            # 验证更新
            updated_memory = memory.get(first_memory_id)
            print(f"更新后的记忆: {updated_memory}")
            print()
        except Exception as e:
            print(f"❌ 更新记忆失败: {e}")
    
    # 7. 删除特定记忆
    if len(all_memories) > 1:
        print("7. 删除特定记忆...")
        try:
            second_memory_id = all_memories[1]['id']
            delete_result = memory.delete(second_memory_id)
            print(f"✓ 删除记忆结果: {delete_result}")
            print()
        except Exception as e:
            print(f"❌ 删除记忆失败: {e}")
    
    # 8. 清理 - 删除所有记忆
    print("8. 清理所有记忆...")
    try:
        cleanup_result = memory.delete_all(user_id=user_id)
        print(f"✓ 清理结果: {cleanup_result}")
        print()
    except Exception as e:
        print(f"❌ 清理失败: {e}")
    
    print("=== 本地模型基础操作测试完成 ===")

def check_ollama_service():
    """检查Ollama服务是否运行"""
    print("=== 检查Ollama服务状态 ===")
    
    import subprocess
    try:
        # 检查Ollama进程
        result = subprocess.run(['pgrep', 'ollama'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Ollama服务正在运行")
            return True
        else:
            print("❌ Ollama服务未运行")
            print("请运行: ollama serve")
            return False
    except Exception as e:
        print(f"❌ 检查Ollama服务失败: {e}")
        return False

def main():
    """主函数"""
    print("Mem0 本地模型测试")
    print("=" * 50)
    
    # 检查Ollama服务
    if not check_ollama_service():
        print("\n启动Ollama服务的方法:")
        print("1. 在新终端运行: ollama serve")
        print("2. 或者在后台运行: nohup ollama serve &")
        return
    
    # 运行测试
    try:
        test_basic_memory_operations_local()
        
        print("\n🎉 本地模型测试完成！")
        print("\n优势:")
        print("- ✅ 完全免费，无需API密钥")
        print("- ✅ 数据隐私，本地处理")
        print("- ✅ 无网络依赖")
        print("\n注意:")
        print("- 本地模型的效果可能不如GPT-4")
        print("- 处理速度取决于你的硬件配置")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
