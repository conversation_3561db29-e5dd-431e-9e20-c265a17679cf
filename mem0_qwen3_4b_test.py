"""
Mem0 + Qwen3:4B 完整测试套件
使用本地Ollama部署的qwen3:4b模型进行Mem0功能测试
这个脚本展示了如何使用更强大的本地模型来获得更好的记忆提取和处理效果
"""

import os
import time
from mem0 import Memory
from typing import Dict, List

def create_qwen3_4b_config():
    """
    创建使用qwen3:4b模型的Mem0配置
    4b参数模型比0.6b有更好的理解和生成能力
    """
    config = {
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "qwen3_4b_memories",
                "path": "./qwen3_4b_chroma_db"
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:4b",
                "temperature": 0.1,  # 较低温度确保一致性
                "max_tokens": 2000,
                "ollama_base_url": "http://localhost:11434"
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:4b",  # 使用同一模型进行嵌入
                "ollama_base_url": "http://localhost:11434"
            }
        }
    }
    return config

def test_basic_memory_operations():
    """测试基础记忆操作"""
    print("=== Mem0 + Qwen3:4B 基础操作测试 ===\n")
    
    # 初始化Memory对象
    print("1. 初始化Memory对象...")
    config = create_qwen3_4b_config()
    
    try:
        memory = Memory.from_config(config)
        print("✓ Memory对象初始化成功")
        print(f"✓ 使用模型: qwen3:4b")
        print(f"✓ 向量数据库: ChromaDB")
        print()
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return None
    
    return memory

def test_chinese_memory_extraction(memory):
    """测试中文记忆提取能力"""
    print("2. 测试中文记忆提取...")
    user_id = "chinese_user"
    
    # 测试复杂的中文对话
    chinese_conversations = [
        {
            "messages": [
                {"role": "user", "content": "你好，我是李明，是一名人工智能研究员。我在清华大学工作，专门研究大语言模型和机器学习。"},
                {"role": "assistant", "content": "你好李明！很高兴认识你。我了解到你是清华大学的AI研究员，专注于大语言模型和机器学习研究。"}
            ],
            "metadata": {"category": "个人信息", "language": "chinese"}
        },
        {
            "messages": [
                {"role": "user", "content": "我平时喜欢喝茶，特别是龙井茶。我也喜欢阅读，最近在读《深度学习》这本书。周末我喜欢去颐和园散步。"},
                {"role": "assistant", "content": "记住了你的爱好：喜欢龙井茶，正在阅读《深度学习》，周末喜欢去颐和园散步。"}
            ],
            "metadata": {"category": "兴趣爱好", "language": "chinese"}
        },
        {
            "messages": [
                {"role": "user", "content": "我目前在做一个关于多模态大模型的项目，希望能够让AI更好地理解图像和文本的关系。这个项目预计需要6个月完成。"},
                {"role": "assistant", "content": "了解了你的研究项目：多模态大模型，专注于图像和文本理解，预计6个月完成。"}
            ],
            "metadata": {"category": "工作项目", "language": "chinese"}
        }
    ]
    
    # 添加记忆
    for i, conv in enumerate(chinese_conversations, 1):
        try:
            result = memory.add(conv["messages"], user_id=user_id, metadata=conv["metadata"])
            print(f"✓ 添加中文记忆 {i}: {result}")
        except Exception as e:
            print(f"❌ 添加记忆 {i} 失败: {e}")
    
    print()
    return user_id

def test_memory_search_capabilities(memory, user_id):
    """测试记忆搜索能力"""
    print("3. 测试记忆搜索能力...")
    
    # 测试不同类型的搜索查询
    search_queries = [
        "李明的职业是什么？",
        "用户在哪里工作？",
        "用户喜欢什么饮品？",
        "用户的研究方向是什么？",
        "用户的项目需要多长时间完成？",
        "用户周末喜欢做什么？"
    ]
    
    for query in search_queries:
        try:
            print(f"\n🔍 搜索: {query}")
            results = memory.search(query, user_id=user_id, limit=3)
            
            if results.get("results"):
                for i, result in enumerate(results["results"], 1):
                    print(f"  {i}. 记忆: {result['memory']}")
                    print(f"     相关性: {result['score']:.4f}")
                    print(f"     元数据: {result.get('metadata', {})}")
            else:
                print("  未找到相关记忆")
        except Exception as e:
            print(f"  ❌ 搜索失败: {e}")
    
    print()

def test_memory_management(memory, user_id):
    """测试记忆管理功能"""
    print("4. 测试记忆管理功能...")
    
    try:
        # 获取所有记忆
        all_memories = memory.get_all(user_id=user_id)
        print(f"✓ 获取到 {len(all_memories)} 条记忆")
        
        # 显示记忆详情
        for i, mem in enumerate(all_memories, 1):
            print(f"  {i}. [{mem['id']}] {mem['memory']}")
            print(f"     分类: {mem.get('metadata', {}).get('category', 'N/A')}")
        
        # 测试记忆更新
        if all_memories:
            print(f"\n更新第一条记忆...")
            first_memory_id = all_memories[0]['id']
            update_result = memory.update(
                memory_id=first_memory_id,
                data="李明是清华大学的高级AI研究员，专门研究多模态大语言模型，在该领域有丰富经验。"
            )
            print(f"✓ 更新结果: {update_result}")
        
        print()
        
    except Exception as e:
        print(f"❌ 记忆管理测试失败: {e}")

def test_complex_conversation_memory(memory):
    """测试复杂对话的记忆能力"""
    print("5. 测试复杂对话记忆...")
    
    user_id = "complex_user"
    
    # 模拟一个复杂的多轮对话
    complex_conversation = [
        {"role": "user", "content": "我想为我的公司开发一个AI客服系统。"},
        {"role": "assistant", "content": "这是个很好的想法！能告诉我更多关于你公司的情况吗？"},
        {"role": "user", "content": "我们是一家电商公司，主要销售电子产品。每天有大量的客户咨询，人工客服压力很大。"},
        {"role": "assistant", "content": "了解了，电商行业确实需要高效的客服系统。你们主要遇到什么类型的客户问题？"},
        {"role": "user", "content": "主要是产品咨询、订单查询、售后服务这三类。我希望AI能处理80%的常见问题。"},
        {"role": "assistant", "content": "这个目标很合理。我建议使用知识图谱和意图识别来构建这个系统。"},
        {"role": "user", "content": "听起来不错。我们的预算大概是50万，希望3个月内上线。"},
        {"role": "assistant", "content": "预算和时间都比较合理。我会帮你制定一个详细的开发计划。"}
    ]
    
    try:
        # 添加整个对话作为记忆
        result = memory.add(
            complex_conversation, 
            user_id=user_id, 
            metadata={
                "category": "项目需求", 
                "project_type": "AI客服系统",
                "industry": "电商",
                "budget": "50万",
                "timeline": "3个月"
            }
        )
        print(f"✓ 复杂对话记忆添加成功: {result}")
        
        # 测试对复杂对话的理解
        complex_queries = [
            "用户的公司是做什么的？",
            "用户想开发什么系统？",
            "项目的预算是多少？",
            "主要的客户问题有哪些？",
            "项目的时间要求是什么？"
        ]
        
        print("\n测试复杂对话理解:")
        for query in complex_queries:
            results = memory.search(query, user_id=user_id, limit=2)
            print(f"\n🔍 {query}")
            for result in results.get("results", []):
                print(f"  - {result['memory']} (相关性: {result['score']:.4f})")
        
        # 清理测试数据
        memory.delete_all(user_id=user_id)
        print(f"\n✓ 复杂对话测试完成，数据已清理")
        
    except Exception as e:
        print(f"❌ 复杂对话测试失败: {e}")

def test_performance_benchmark(memory):
    """性能基准测试"""
    print("\n6. 性能基准测试...")
    
    user_id = "benchmark_user"
    
    # 准备测试数据
    test_conversations = []
    for i in range(5):
        conv = [
            {"role": "user", "content": f"这是第{i+1}个测试对话，我想测试系统的性能。"},
            {"role": "assistant", "content": f"收到第{i+1}个测试消息，系统运行正常。"}
        ]
        test_conversations.append(conv)
    
    # 测试添加记忆的性能
    print("测试添加记忆性能...")
    start_time = time.time()
    
    for i, conv in enumerate(test_conversations):
        try:
            memory.add(conv, user_id=user_id, metadata={"test_id": i+1})
        except Exception as e:
            print(f"添加记忆 {i+1} 失败: {e}")
    
    add_time = time.time() - start_time
    print(f"✓ 添加 {len(test_conversations)} 条记忆耗时: {add_time:.2f} 秒")
    
    # 测试搜索性能
    print("测试搜索性能...")
    start_time = time.time()
    
    for i in range(3):
        try:
            memory.search(f"第{i+1}个测试", user_id=user_id)
        except Exception as e:
            print(f"搜索 {i+1} 失败: {e}")
    
    search_time = time.time() - start_time
    print(f"✓ 执行 3 次搜索耗时: {search_time:.2f} 秒")
    
    # 清理测试数据
    memory.delete_all(user_id=user_id)
    print("✓ 性能测试完成，数据已清理")

def main():
    """主函数"""
    print("Mem0 + Qwen3:4B 完整测试套件")
    print("=" * 60)
    print("使用本地Ollama qwen3:4b模型进行Mem0功能测试")
    print("4B参数模型提供更好的理解和生成能力")
    print("=" * 60)
    
    # 检查Ollama服务
    import subprocess
    try:
        result = subprocess.run(['pgrep', 'ollama'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Ollama服务未运行，请先启动: ollama serve")
            return
    except:
        print("⚠️  无法检查Ollama服务状态")
    
    # 运行测试
    try:
        # 基础操作测试
        memory = test_basic_memory_operations()
        if not memory:
            return
        
        # 中文记忆提取测试
        user_id = test_chinese_memory_extraction(memory)
        
        # 搜索能力测试
        test_memory_search_capabilities(memory, user_id)
        
        # 记忆管理测试
        test_memory_management(memory, user_id)
        
        # 复杂对话测试
        test_complex_conversation_memory(memory)
        
        # 性能基准测试
        test_performance_benchmark(memory)
        
        # 清理主测试数据
        memory.delete_all(user_id=user_id)
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print("\n✅ 测试结果总结:")
        print("- Qwen3:4B模型成功集成到Mem0")
        print("- 中文记忆提取和理解能力良好")
        print("- 搜索功能正常工作")
        print("- 记忆管理功能完整")
        print("- 复杂对话处理能力强")
        print("\n💡 优势:")
        print("- 🆓 完全免费，无API费用")
        print("- 🔒 数据隐私，本地处理")
        print("- 🚀 无网络依赖")
        print("- 🧠 4B参数提供更好的理解能力")
        print("- 🇨🇳 对中文支持良好")
        
        print(f"\n📊 模型信息:")
        print(f"- 模型: qwen3:4b")
        print(f"- 参数量: 4B")
        print(f"- 模型大小: ~2.5GB")
        print(f"- 向量数据库: ChromaDB")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
