"""
Mem0 智能聊天系统测试用例
演示如何构建一个具有记忆功能的聊天机器人
这个系统能够记住用户的偏好、历史对话，并在后续对话中使用这些信息
"""

import os
from dotenv import load_dotenv
from mem0 import Memory
from openai import OpenAI

# 加载环境变量
load_dotenv()

class MemoryEnabledChatBot:
    """
    具有记忆功能的聊天机器人
    能够记住用户信息并在对话中使用历史记忆
    """
    
    def __init__(self):
        """初始化聊天机器人"""
        self.memory = Memory()
        self.openai_client = OpenAI()
        self.conversation_history = []
    
    def get_relevant_memories(self, message: str, user_id: str, limit: int = 3) -> str:
        """
        获取与当前消息相关的历史记忆
        
        Args:
            message: 用户当前的消息
            user_id: 用户ID
            limit: 返回的记忆数量限制
            
        Returns:
            格式化的记忆字符串
        """
        try:
            # 搜索相关记忆
            search_results = self.memory.search(
                query=message, 
                user_id=user_id, 
                limit=limit
            )
            
            if not search_results.get("results"):
                return "暂无相关历史记忆。"
            
            # 格式化记忆
            memories = []
            for result in search_results["results"]:
                memory_text = result["memory"]
                score = result["score"]
                # 只包含相关性较高的记忆（分数 > 0.3）
                if score > 0.3:
                    memories.append(f"- {memory_text}")
            
            if not memories:
                return "暂无高相关性的历史记忆。"
            
            return "相关历史记忆:\n" + "\n".join(memories)
            
        except Exception as e:
            print(f"获取记忆时出错: {e}")
            return "获取历史记忆失败。"
    
    def generate_response(self, message: str, user_id: str) -> str:
        """
        生成基于记忆的回复
        
        Args:
            message: 用户消息
            user_id: 用户ID
            
        Returns:
            AI生成的回复
        """
        # 获取相关记忆
        relevant_memories = self.get_relevant_memories(message, user_id)
        
        # 构建系统提示词
        system_prompt = f"""你是一个智能助手，具有记忆功能。请基于用户的历史记忆来回答问题。

{relevant_memories}

请根据上述记忆信息来回答用户的问题。如果记忆中有相关信息，请自然地引用它们。
如果没有相关记忆，就正常回答问题。保持友好和有帮助的语调。"""

        # 构建对话消息
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": message}
        ]
        
        try:
            # 调用OpenAI API
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            print(f"生成回复时出错: {e}")
            return "抱歉，我现在无法回复。请稍后再试。"
    
    def chat(self, message: str, user_id: str) -> str:
        """
        处理用户消息并返回回复
        
        Args:
            message: 用户消息
            user_id: 用户ID
            
        Returns:
            AI回复
        """
        # 生成回复
        response = self.generate_response(message, user_id)
        
        # 构建对话记录
        conversation = [
            {"role": "user", "content": message},
            {"role": "assistant", "content": response}
        ]
        
        # 将对话添加到记忆中
        try:
            self.memory.add(
                conversation, 
                user_id=user_id,
                metadata={
                    "session_type": "chat",
                    "timestamp": str(os.times())
                }
            )
        except Exception as e:
            print(f"保存记忆时出错: {e}")
        
        return response

def test_memory_chat_system():
    """测试记忆聊天系统"""
    print("=== Mem0 智能聊天系统测试 ===\n")
    
    # 初始化聊天机器人
    chatbot = MemoryEnabledChatBot()
    user_id = "test_user_chat"
    
    print("聊天机器人已启动！正在进行测试对话...\n")
    
    # 模拟对话序列
    conversations = [
        "你好！我是张三，我是一名软件工程师。",
        "我喜欢喝咖啡，特别是拿铁。我通常在早上9点喝第一杯咖啡。",
        "我正在学习机器学习，目前在研究深度学习算法。",
        "我计划下个月去上海出差，需要待一周。",
        "你还记得我的名字吗？",
        "我喜欢什么饮品？",
        "我在学习什么技术？",
        "我的出差计划是什么？",
        "根据我的偏好，你能推荐一些适合我的咖啡厅吗？",
        "考虑到我在学习机器学习，你能推荐一些相关的书籍吗？"
    ]
    
    # 进行对话测试
    for i, user_message in enumerate(conversations, 1):
        print(f"对话 {i}:")
        print(f"用户: {user_message}")
        
        # 获取AI回复
        ai_response = chatbot.chat(user_message, user_id)
        print(f"AI: {ai_response}")
        print("-" * 50)
    
    # 显示所有记忆
    print("\n=== 聊天过程中积累的记忆 ===")
    all_memories = chatbot.memory.get_all(user_id=user_id)
    
    for i, memory in enumerate(all_memories, 1):
        print(f"{i}. {memory['memory']}")
        print(f"   创建时间: {memory.get('created_at', 'N/A')}")
        print(f"   元数据: {memory.get('metadata', {})}")
        print()
    
    # 测试记忆搜索功能
    print("=== 记忆搜索测试 ===")
    search_queries = [
        "用户的个人信息",
        "用户的饮食偏好",
        "用户的学习内容",
        "用户的旅行计划"
    ]
    
    for query in search_queries:
        print(f"\n搜索: {query}")
        results = chatbot.memory.search(query, user_id=user_id)
        for result in results.get("results", [])[:3]:
            print(f"  - {result['memory']} (相关性: {result['score']:.4f})")
    
    # 清理测试数据
    print(f"\n清理测试数据...")
    chatbot.memory.delete_all(user_id=user_id)
    print("✓ 测试数据已清理")
    
    print("\n=== 智能聊天系统测试完成 ===")

def interactive_chat_demo():
    """
    交互式聊天演示
    用户可以实际与具有记忆功能的聊天机器人对话
    """
    print("=== 交互式记忆聊天演示 ===\n")
    print("你现在可以与具有记忆功能的AI聊天机器人对话！")
    print("机器人会记住你说过的话，并在后续对话中使用这些信息。")
    print("输入 'quit' 或 'exit' 退出聊天。")
    print("输入 'memories' 查看当前记忆。")
    print("-" * 50)
    
    chatbot = MemoryEnabledChatBot()
    user_id = "interactive_user"
    
    while True:
        try:
            user_input = input("\n你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见！")
                break
            
            if user_input.lower() == 'memories':
                memories = chatbot.memory.get_all(user_id=user_id)
                print(f"\n当前记忆 ({len(memories)} 条):")
                for i, mem in enumerate(memories, 1):
                    print(f"{i}. {mem['memory']}")
                continue
            
            if not user_input:
                continue
            
            # 获取AI回复
            response = chatbot.chat(user_input, user_id)
            print(f"AI: {response}")
            
        except KeyboardInterrupt:
            print("\n\n再见！")
            break
        except Exception as e:
            print(f"出现错误: {e}")
    
    # 清理
    chatbot.memory.delete_all(user_id=user_id)

def main():
    """主函数"""
    if not os.getenv("OPENAI_API_KEY"):
        print("错误: 请设置OPENAI_API_KEY环境变量")
        return
    
    # 运行自动测试
    test_memory_chat_system()
    
    # 询问是否运行交互式演示
    print("\n是否要运行交互式聊天演示？(y/n): ", end="")
    choice = input().strip().lower()
    
    if choice in ['y', 'yes', '是']:
        interactive_chat_demo()

if __name__ == "__main__":
    main()
