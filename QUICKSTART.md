# Mem0 快速入门指南

## 🚀 5分钟快速体验

### 1. 环境准备
```bash
# 创建conda环境
conda create -n mem0_test python=3.10 -y
conda activate mem0_test

# 安装依赖
pip install -r requirements.txt
```

### 2. 立即体验（无需API密钥）
```bash
python demo_without_api.py
```

这个演示脚本会展示：
- ✅ Mem0的核心概念
- ✅ 基础操作流程
- ✅ 多用户记忆隔离
- ✅ 记忆的生命周期

### 3. 完整功能体验（需要API密钥）

#### 3.1 配置API密钥
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，添加你的OpenAI API密钥
# OPENAI_API_KEY=sk-your-actual-api-key-here
```

#### 3.2 运行完整测试套件
```bash
python run_all_tests.py
```

## 📚 学习路径

### 阶段1: 理解概念
- 📖 阅读 `README.md` 了解Mem0基础概念
- 🎯 运行 `demo_without_api.py` 体验核心功能

### 阶段2: 基础操作
- 🔧 运行 `01_basic_memory_operations.py` 学习基本API
- ⚙️ 运行 `02_advanced_configuration.py` 了解配置选项

### 阶段3: 实际应用
- 💬 运行 `03_memory_chat_system.py` 构建聊天机器人
- 👥 运行 `04_multi_user_memory.py` 管理多用户记忆

## 🎯 核心概念速览

### 记忆操作
```python
from mem0 import Memory

memory = Memory()

# 添加记忆
memory.add(messages, user_id="user123")

# 搜索记忆
results = memory.search("查询内容", user_id="user123")

# 获取所有记忆
all_memories = memory.get_all(user_id="user123")

# 更新记忆
memory.update(memory_id="mem_id", data="新内容")

# 删除记忆
memory.delete(memory_id="mem_id")
```

### 配置选项
```python
config = {
    "vector_store": {"provider": "chroma"},
    "llm": {"provider": "openai", "config": {"model": "gpt-4o-mini"}},
    "embedder": {"provider": "openai"}
}

memory = Memory.from_config(config)
```

## 🛠️ 常见问题

### Q: 没有API密钥可以学习吗？
A: 可以！运行 `demo_without_api.py` 了解所有核心概念。

### Q: 推荐的学习顺序是什么？
A: 
1. `demo_without_api.py` - 理解概念
2. `01_basic_memory_operations.py` - 学习基础
3. `02_advanced_configuration.py` - 掌握配置
4. `03_memory_chat_system.py` - 实际应用
5. `04_multi_user_memory.py` - 高级功能

### Q: 如何获取OpenAI API密钥？
A: 访问 [OpenAI Platform](https://platform.openai.com/) 注册并创建API密钥。

### Q: 有其他LLM选择吗？
A: 是的！Mem0支持多种LLM提供商，包括Anthropic、Google AI、本地Ollama等。

## 🎉 下一步

学完这些测试用例后，你可以：

1. **构建自己的记忆系统**
   - 参考 `03_memory_chat_system.py` 的聊天机器人实现
   - 根据你的需求定制记忆提取逻辑

2. **探索高级功能**
   - 图记忆(Graph Memory)
   - 多模态记忆
   - 自定义提示词

3. **生产环境部署**
   - 选择合适的向量数据库
   - 配置记忆持久化
   - 实现用户权限管理

## 📖 更多资源

- [Mem0 官方文档](https://docs.mem0.ai/)
- [GitHub 仓库](https://github.com/mem0ai/mem0)
- [社区讨论](https://github.com/mem0ai/mem0/discussions)

---

**开始你的Mem0学习之旅吧！** 🚀
