# Mem0 学习测试项目

这个项目包含了系统学习Mem0框架的测试用例和示例代码，帮助你深入理解Mem0的使用方法和核心概念。

## 项目结构

```
mem0_test/
├── README.md                          # 项目说明文档
├── requirements.txt                   # Python依赖包
├── .env.example                       # 环境变量示例
├── .env                              # 环境变量配置（需要自己创建）
├── demo_without_api.py               # 无需API密钥的演示脚本
├── 01_basic_memory_operations.py     # 基础记忆操作测试
├── 02_advanced_configuration.py      # 高级配置测试
├── 03_memory_chat_system.py          # 智能聊天系统
└── 04_multi_user_memory.py          # 多用户记忆管理
```

## 什么是Mem0？

Mem0是一个为AI应用提供持久化、上下文感知记忆的框架。它的主要特点包括：

### 🧠 核心功能
- **自动记忆提取**: 从对话中智能提取关键信息
- **语义搜索**: 基于意义而非关键词的搜索
- **记忆管理**: 支持添加、更新、删除记忆
- **用户隔离**: 每个用户的记忆独立存储
- **多模态支持**: 支持文本、图像等多种数据类型

### 🎯 应用场景
- 个性化聊天机器人
- 用户偏好学习系统
- 上下文感知的AI助手
- 知识管理系统
- 客户服务系统

## 快速开始

### 1. 环境设置

```bash
# 创建conda环境
conda create -n mem0_test python=3.10 -y
conda activate mem0_test

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，添加你的API密钥：
```
OPENAI_API_KEY=sk-your-openai-api-key-here
```

### 3. 运行演示

#### 无需API密钥的演示（推荐先运行）
```bash
python demo_without_api.py
```

这个脚本展示了Mem0的核心概念，无需真实的API密钥。

#### 基础操作测试
```bash
python 01_basic_memory_operations.py
```

## 测试用例详解

### 1. demo_without_api.py - 概念演示
- **目的**: 无需API密钥展示Mem0核心概念
- **内容**: 
  - 记忆的生命周期（添加、搜索、更新、删除）
  - 多用户记忆隔离
  - 基本操作流程

### 2. 01_basic_memory_operations.py - 基础操作
- **目的**: 学习Mem0的基本API使用
- **内容**:
  - Memory对象初始化
  - 添加对话记忆
  - 搜索相关记忆
  - 获取所有记忆
  - 更新和删除记忆

### 3. 02_advanced_configuration.py - 高级配置
- **目的**: 学习Mem0的高级配置选项
- **内容**:
  - ChromaDB向量数据库配置
  - 自定义LLM配置
  - 元数据和过滤功能
  - 不同嵌入模型的使用

### 4. 03_memory_chat_system.py - 智能聊天系统
- **目的**: 构建具有记忆功能的聊天机器人
- **内容**:
  - 记忆增强的对话系统
  - 上下文感知回复生成
  - 交互式聊天演示
  - 记忆在对话中的应用

### 5. 04_multi_user_memory.py - 多用户管理
- **目的**: 学习多用户环境下的记忆管理
- **内容**:
  - 用户注册和管理
  - 记忆隔离机制
  - 用户间记忆比较
  - 批量操作和统计

## Mem0 核心概念

### 记忆的生命周期

1. **添加(Add)**: 
   ```python
   memory.add(messages, user_id="user123", metadata={"category": "preferences"})
   ```

2. **搜索(Search)**:
   ```python
   results = memory.search("用户喜欢什么？", user_id="user123")
   ```

3. **更新(Update)**:
   ```python
   memory.update(memory_id="mem_123", data="更新后的记忆内容")
   ```

4. **删除(Delete)**:
   ```python
   memory.delete(memory_id="mem_123")
   memory.delete_all(user_id="user123")  # 删除用户所有记忆
   ```

### 配置选项

Mem0支持多种配置选项：

```python
config = {
    "vector_store": {
        "provider": "chroma",  # 向量数据库
        "config": {"collection_name": "memories", "path": "./db"}
    },
    "llm": {
        "provider": "openai",  # LLM提供商
        "config": {"model": "gpt-4o-mini", "temperature": 0.1}
    },
    "embedder": {
        "provider": "openai",  # 嵌入模型
        "config": {"model": "text-embedding-3-small"}
    }
}
```

## 最佳实践

### 1. 记忆组织
- 使用有意义的`user_id`来隔离不同用户的记忆
- 通过`metadata`为记忆添加分类和标签
- 定期清理过时的记忆

### 2. 搜索优化
- 使用具体的查询词而非过于宽泛的搜索
- 利用元数据过滤来缩小搜索范围
- 根据相关性分数筛选结果

### 3. 性能考虑
- 选择合适的向量数据库（本地开发用ChromaDB，生产环境考虑Qdrant）
- 合理设置嵌入模型的维度
- 定期清理不需要的记忆

## 常见问题

### Q: 如何选择合适的LLM模型？
A: 
- 开发测试：使用`gpt-4o-mini`（成本低，速度快）
- 生产环境：使用`gpt-4o`（质量高，理解能力强）
- 本地部署：考虑使用Ollama等本地模型

### Q: 记忆存储在哪里？
A: 
- 向量数据库：存储记忆的向量表示（用于搜索）
- 关系数据库：存储记忆的元数据和历史
- 本地文件：ChromaDB等轻量级方案

### Q: 如何处理记忆冲突？
A: Mem0会自动处理记忆的更新和去重，当检测到冲突信息时会智能合并或更新现有记忆。

## 进阶学习

1. **图记忆(Graph Memory)**: 学习如何使用Neo4j等图数据库存储复杂的关系记忆
2. **自定义提示词**: 定制记忆提取和更新的提示词
3. **多模态记忆**: 处理文本、图像等多种类型的记忆
4. **记忆分析**: 分析用户记忆模式和趋势

## 参考资源

- [Mem0 官方文档](https://docs.mem0.ai/)
- [Mem0 GitHub仓库](https://github.com/mem0ai/mem0)
- [OpenAI API文档](https://platform.openai.com/docs)

## 贡献

欢迎提交Issue和Pull Request来改进这个学习项目！

## 许可证

MIT License
