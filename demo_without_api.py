"""
Mem0 演示脚本 - 无需API密钥版本
这个脚本演示了Mem0的基本概念和使用方法，无需真实的API密钥
"""

import os
from typing import Dict, List

# 模拟的Memory类，用于演示概念
class MockMemory:
    """
    模拟的Memory类，用于演示Mem0的基本概念
    实际使用时应该使用真实的mem0.Memory类
    """
    
    def __init__(self):
        self.memories = {}  # 存储记忆的字典 {user_id: [memories]}
        self.memory_counter = 0
    
    def add(self, messages: List[Dict], user_id: str, metadata: Dict = None):
        """模拟添加记忆"""
        if user_id not in self.memories:
            self.memories[user_id] = []
        
        # 模拟从对话中提取记忆
        extracted_memories = []
        for message in messages:
            if message["role"] == "user":
                # 简单的记忆提取逻辑（实际中由LLM完成）
                content = message["content"]
                if "我是" in content or "我叫" in content:
                    extracted_memories.append(f"用户身份信息: {content}")
                elif "喜欢" in content:
                    extracted_memories.append(f"用户偏好: {content}")
                elif "住在" in content or "来自" in content:
                    extracted_memories.append(f"用户位置信息: {content}")
                else:
                    extracted_memories.append(f"用户信息: {content}")
        
        # 添加记忆
        for memory_text in extracted_memories:
            self.memory_counter += 1
            memory = {
                "id": f"mem_{self.memory_counter}",
                "memory": memory_text,
                "user_id": user_id,
                "metadata": metadata or {},
                "created_at": "2024-01-01T00:00:00Z"
            }
            self.memories[user_id].append(memory)
        
        return {"results": [{"memory": mem, "event": "ADD"} for mem in extracted_memories]}
    
    def search(self, query: str, user_id: str, limit: int = 5):
        """模拟搜索记忆"""
        if user_id not in self.memories:
            return {"results": []}
        
        # 简单的关键词匹配搜索
        results = []
        for memory in self.memories[user_id]:
            score = 0.0
            query_words = query.lower().split()
            memory_text = memory["memory"].lower()
            
            # 计算简单的相关性分数
            for word in query_words:
                if word in memory_text:
                    score += 0.3
            
            if score > 0:
                results.append({
                    **memory,
                    "score": min(score, 1.0)
                })
        
        # 按分数排序
        results.sort(key=lambda x: x["score"], reverse=True)
        return {"results": results[:limit]}
    
    def get_all(self, user_id: str):
        """获取用户所有记忆"""
        return self.memories.get(user_id, [])
    
    def update(self, memory_id: str, data: str):
        """模拟更新记忆"""
        for user_memories in self.memories.values():
            for memory in user_memories:
                if memory["id"] == memory_id:
                    memory["memory"] = data
                    return {"message": "Memory updated successfully!"}
        return {"error": "Memory not found"}
    
    def delete(self, memory_id: str):
        """模拟删除记忆"""
        for user_memories in self.memories.values():
            for i, memory in enumerate(user_memories):
                if memory["id"] == memory_id:
                    del user_memories[i]
                    return {"message": "Memory deleted successfully!"}
        return {"error": "Memory not found"}
    
    def delete_all(self, user_id: str):
        """删除用户所有记忆"""
        if user_id in self.memories:
            del self.memories[user_id]
        return {"message": f"All memories for user {user_id} deleted"}

def demo_basic_operations():
    """演示基础操作"""
    print("=== Mem0 基础操作演示 ===\n")
    
    # 初始化模拟的Memory对象
    memory = MockMemory()
    user_id = "demo_user"
    
    print("1. 添加记忆...")
    
    # 添加用户基本信息
    messages1 = [
        {"role": "user", "content": "你好，我是张三，我是一名软件工程师。"},
        {"role": "assistant", "content": "你好张三！很高兴认识你。"}
    ]
    
    result1 = memory.add(messages1, user_id=user_id, metadata={"category": "personal_info"})
    print(f"添加结果: {result1}")
    print()
    
    # 添加用户偏好
    messages2 = [
        {"role": "user", "content": "我喜欢喝咖啡，特别是拿铁。我住在北京。"},
        {"role": "assistant", "content": "记住了你的偏好和位置信息。"}
    ]
    
    result2 = memory.add(messages2, user_id=user_id, metadata={"category": "preferences"})
    print(f"添加偏好: {result2}")
    print()
    
    print("2. 搜索记忆...")
    
    # 搜索用户信息
    search_results = memory.search("张三的职业是什么？", user_id=user_id)
    print("搜索 '张三的职业是什么？':")
    for result in search_results["results"]:
        print(f"  - {result['memory']} (相关性: {result['score']:.2f})")
    print()
    
    # 搜索偏好信息
    preference_results = memory.search("用户喜欢什么饮品？", user_id=user_id)
    print("搜索 '用户喜欢什么饮品？':")
    for result in preference_results["results"]:
        print(f"  - {result['memory']} (相关性: {result['score']:.2f})")
    print()
    
    print("3. 获取所有记忆...")
    all_memories = memory.get_all(user_id=user_id)
    print(f"用户 {user_id} 的所有记忆:")
    for i, mem in enumerate(all_memories, 1):
        print(f"  {i}. [{mem['id']}] {mem['memory']}")
        print(f"     元数据: {mem['metadata']}")
    print()
    
    print("4. 更新记忆...")
    if all_memories:
        first_memory_id = all_memories[0]["id"]
        update_result = memory.update(first_memory_id, "用户张三是一名高级软件工程师，专注于Python开发。")
        print(f"更新结果: {update_result}")
        
        # 验证更新
        updated_memories = memory.get_all(user_id=user_id)
        print(f"更新后的记忆: {updated_memories[0]['memory']}")
    print()
    
    print("5. 删除记忆...")
    if len(all_memories) > 1:
        second_memory_id = all_memories[1]["id"]
        delete_result = memory.delete(second_memory_id)
        print(f"删除结果: {delete_result}")
    print()
    
    print("6. 清理所有记忆...")
    cleanup_result = memory.delete_all(user_id=user_id)
    print(f"清理结果: {cleanup_result}")
    
    print("\n=== 基础操作演示完成 ===")

def demo_multi_user_scenario():
    """演示多用户场景"""
    print("\n=== 多用户场景演示 ===\n")
    
    memory = MockMemory()
    
    # 用户A的信息
    user_a = "alice"
    messages_a = [
        {"role": "user", "content": "我是Alice，我喜欢阅读科幻小说。"},
        {"role": "assistant", "content": "了解了，Alice喜欢科幻小说。"}
    ]
    memory.add(messages_a, user_id=user_a, metadata={"category": "hobbies"})
    
    # 用户B的信息
    user_b = "bob"
    messages_b = [
        {"role": "user", "content": "我是Bob，我喜欢运动，特别是篮球。"},
        {"role": "assistant", "content": "记住了，Bob喜欢篮球运动。"}
    ]
    memory.add(messages_b, user_id=user_b, metadata={"category": "hobbies"})
    
    print("多用户记忆隔离测试:")
    print(f"Alice的记忆: {memory.get_all(user_a)}")
    print(f"Bob的记忆: {memory.get_all(user_b)}")
    
    # 搜索测试
    print("\n搜索 '喜欢什么' 在不同用户中的结果:")
    alice_results = memory.search("喜欢什么", user_id=user_a)
    bob_results = memory.search("喜欢什么", user_id=user_b)
    
    print(f"Alice的搜索结果: {[r['memory'] for r in alice_results['results']]}")
    print(f"Bob的搜索结果: {[r['memory'] for r in bob_results['results']]}")
    
    print("\n=== 多用户场景演示完成 ===")

def demo_memory_concepts():
    """演示Mem0的核心概念"""
    print("\n=== Mem0 核心概念演示 ===\n")
    
    print("1. 记忆的生命周期:")
    print("   - 添加(Add): 从对话中提取并存储新的记忆")
    print("   - 搜索(Search): 基于查询找到相关的历史记忆")
    print("   - 更新(Update): 修改现有记忆的内容")
    print("   - 删除(Delete): 移除不需要的记忆")
    print()
    
    print("2. 记忆的组织:")
    print("   - 用户隔离: 每个用户的记忆是独立的")
    print("   - 元数据: 可以为记忆添加分类、标签等信息")
    print("   - 时间戳: 记录记忆的创建和更新时间")
    print()
    
    print("3. 记忆的应用场景:")
    print("   - 个性化聊天机器人")
    print("   - 用户偏好学习")
    print("   - 上下文感知的AI助手")
    print("   - 知识管理系统")
    print()
    
    print("4. Mem0的优势:")
    print("   - 自动记忆提取: 从对话中智能提取关键信息")
    print("   - 语义搜索: 基于意义而非关键词的搜索")
    print("   - 记忆去重: 避免存储重复信息")
    print("   - 记忆更新: 智能合并和更新相关记忆")
    print()

def main():
    """主函数"""
    print("欢迎使用Mem0学习演示系统！")
    print("这个演示展示了Mem0的核心概念和基本用法。")
    print("=" * 50)
    
    # 演示核心概念
    demo_memory_concepts()
    
    # 演示基础操作
    demo_basic_operations()
    
    # 演示多用户场景
    demo_multi_user_scenario()
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("\n要使用真实的Mem0功能，请:")
    print("1. 获取OpenAI API密钥")
    print("2. 在.env文件中设置OPENAI_API_KEY")
    print("3. 运行其他测试脚本 (01_basic_memory_operations.py 等)")
    print("\n更多信息请参考: https://docs.mem0.ai/")

if __name__ == "__main__":
    main()
